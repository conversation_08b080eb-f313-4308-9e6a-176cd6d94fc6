# 智能体资源中心与消息系统

## 概览

本方案为 Koishi 智能体（Agent）设计了一个集成的资源管理与消息处理系统。其核心目的是解决在与 LLM 交互时遇到的两大挑战：
1.  **资源瞬时性**：聊天平台中的资源链接（如图片、文件）通常是临时的，直接存入长期记忆或上下文会导致其失效。
2.  **上下文不友好**：Koishi 丰富的消息元素（Element）格式对于 LLM 来说难以直接理解和利用，特别是对于多模态输入。

本系统通过引入**资源中心 (Asset Service)** 和**消息编解码器 (Message Transformer)**，将平台消息与对 LLM 友好的格式进行双向转换，同时赋予了智能体理解、交互和生成各类丰富资源的能力。

## 1. 资源中心 (Asset Service)

资源中心是整个系统的基石，负责管理所有非临时性资源（图片、音视频、文件）的生命周期。我们将它设计为一个 Koishi 服务，插件可以通过 `ctx.assets` 进行调用。

### 1.1 核心目标

*   **持久化存储**：将来自不同平台的临时资源（如有时效性的下载链接）下载并保存到本地或远程存储中。
*   **统一标识**：为每个持久化的资源分配一个唯一的、稳定的内部 ID（例如 UUID），并利用哈希去重。
*   **访问抽象**：无论资源存储在何处（本地、S3、OSS等），都提供统一的 API 进行访问。
*   **生命周期管理**：实现资源的自动清理和过期策略，防止空间无限膨胀。
*   **智能体交互**：为 AI Agent 提供查看和理解资源内容的工具。

### 1.2 数据库模型

我们需要一个数据库表来存储资源的元信息。推荐使用 `@koishijs/plugin-database` 提供的模型扩展能力。

**`assets` 表结构:**

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `string` | **主键**，唯一的内部资源 ID (e.g., UUID)。 |
| `type` | `string` | 资源类型，如 `image`, `audio`, `video`, `file`。 |
| `mime` | `string` | 资源的 MIME 类型，如 `image/png`, `application/pdf`。 |
| `hash` | `string` | 文件内容的 SHA256 哈希，用于去重。 |
| `size` | `integer` | 文件大小（字节）。 |
| `createdAt` | `timestamp` | 创建时间。 |
| `lastUsedAt` | `timestamp` | 最后使用时间（每次发送或访问时更新）。 |
| `metadata` | `json` | 可选的元数据，用于存储文件名、原始URL、图片宽高、视频时长等信息。 |

### 1.3 核心 API (`ctx.assets`)

*   `ctx.assets.create(urlOrBuffer, type?, filename?)`: 创建一个资源。内部处理下载、计算哈希、查重、存入数据库和存储系统，最终返回内部ID。
*   `ctx.assets.get(id)`: 获取资源元信息。
*   `ctx.assets.read(id)`: 读取资源的二进制 Buffer。
*   `ctx.assets.getPublicUrl(id)`: 获取资源的公开访问链接（如果配置了公网访问）。
*   `ctx.assets.delete(id)`: 删除一个资源。

### 1.4 资源存储接口 (StorageDriver) <Badge type="primary">可扩展</Badge>

为了支持本地和远程存储，我们采用驱动 (Driver) 模式。

```ts
interface StorageDriver {
  write(id: string, buffer: Buffer): Promise<void>;
  read(id: string): Promise<Buffer>;
  delete(id: string): Promise<void>;
  getPublicUrl?(id: string): string;
}
```
内置一个基于本地文件的 `LocalStorageDriver`，并允许通过插件注册其他驱动（如 S3Driver）。

### 1.5 智能体资源交互 (Agent Tools)

这些工具将作为函数提供给 LLM，使其能够与资源中心交互。

*   **`assets.view_file(id: string): Promise<string>`**: 查看文件内容。
    *   **功能**: 这是最重要的工具。它会根据文件类型，以对 LLM 最友好的方式返回内容。
    *   **实现细节**:
        *   **文本文件** (`.txt`, `.md`, `.json`): 直接返回前 4000 个字符的文本内容。
        *   **PDF/Word**: 使用 `pdf-parse`, `mammoth` 等库提取纯文本内容并返回。
        *   **图片**: 若具备多模态能力 (如 GPT-4V)，进行识图并返回图片描述。若无，则返回元信息：“[图片: 文件名 'cat.jpg', 类型 'image/jpeg', 大小 128KB]”。
        *   **压缩包**: 返回 “[压缩包: 文件名 'archive.zip']，请使用 `assets.list_archive` 工具查看其内容。”
        *   **其他二进制文件**: 返回 “[二进制文件: 文件名 'data.bin']，无法预览内容。”

*   **`assets.list_archive(id: string): Promise<string>`**: 列出压缩包内容。

### 1.6 过期资源清理

*   **机制**: 一个定时任务（例如每天凌晨执行的 `ctx.cron`）。
*   **逻辑**: `DELETE FROM assets WHERE lastUsedAt < NOW() - '30 days'`。同时，从存储系统中调用 `driver.delete(id)` 删除对应的物理文件。

## 2. 消息编解码器 (Message Transformer)

这一层负责在 **平台原生消息** 和 **包含内部资源ID的、对LLM友好的消息** 之间进行双向转换。

### 2.1 编码流程 (平台消息 → LLM 格式)

当收到用户的消息时，在将其存入历史记录或喂给 LLM 之前进行转换。此流程分两步完成。

#### 2.1.1 资源持久化

使用一个高优先级的中间件，将消息中的临时资源链接转换为内部ID。

```ts
import { Context, h } from 'koishi';

export function apply(ctx: Context) {
  const rules: h.AsyncTransformer = {
    // 转换所有资源元素
    async img(attrs) {
      if (!attrs.src || attrs.id) return h('img', attrs);
      const id = await ctx.assets.create(attrs.src, 'image');
      // 保留重要元信息，丢弃临时的src
      return h('img', { id, width: attrs.width, height: attrs.height });
    },
    // ... 对 audio, video, file 进行类似处理
  };

  ctx.middleware(async (session, next) => {
    session.elements = await h.transformAsync(session.elements, rules);
    return next();
  }, true); // `true` 表示这是前置中间件
}
```

#### 2.1.2 转换为 LLM 输入格式

在将持久化后的消息传递给 LLM 之前，进行最终格式化。

*   **多模态消息转换**：对于支持图片输入的模型，需要将 `<img id="..."/>` 转换为模型接受的格式。
    *   **流程**：
        1.  遍历消息元素，分离出文本部分和图片部分。
        2.  对于每个 `<img id="..."/>` 元素，调用 `ctx.assets.read(id)` 获取图片 Buffer。
        3.  **图片处理**：为减少 Token 消耗，使用 `sharp` 等库对图片进行压缩或缩放。
        4.  **GIF 处理**：可抽取关键帧（如第1、中、末帧）作为多张静态图，或仅使用第一帧。
        5.  将处理后的图片 Buffer 编码为 Base64，与文本部分组合成 LLM API 所需的 `content` 数组格式（如 `[{ type: 'text', ... }, { type: 'image_url', ... }]`）。

*   **其他元素格式化**：对于 LLM 无法直接理解的元素（如自定义的 `<json>`），将其格式化为可读的纯文本。
    ```ts
    const llmFormatRules: h.Transformer = {
      json: (attrs, children) => {
        const content = children.map(el => el.attrs.content).join('');
        return h('text', { content: `\n\`\`\`json\n${content}\n\`\`\`\n` });
      },
    };
    const llmFriendlyElements = h.transform(persistedElements, llmFormatRules);
    ```

### 2.2 解码流程 (LLM 回复 → 平台消息)

当 LLM 生成回复并准备发送时，需要将内部 ID 或自定义指令转换回平台可识别的消息元素。

#### 2.2.1 内部 ID 转换

对于标准资源元素，将内部 ID 转换为可访问的 `src`。
```ts
const decodingRules: h.AsyncTransformer = {
    async img(attrs) {
        if (!attrs.id) return h('img', attrs);
        // 优先使用公网URL，若无则使用base64
        const src = await ctx.assets.getPublicUrl(attrs.id)
            || `data:image/png;base64,${(await ctx.assets.read(attrs.id)).toString('base64')}`;
        return h('img', { ...attrs, src });
    },
    // ... 对其他资源元素做类似处理
};
// 在发送前进行转换
const messageToSend = await h.transformAsync(llmResponseElements, decodingRules);
session.send(messageToSend);
```

#### 2.2.2 自定义组件实现

利用 Koishi 的组件系统，让 LLM 可以通过简单的标签执行复杂操作。

*   **实现**: 使用 `ctx.component()` 注册全局组件。
```ts
// 注册一个发送表情包的组件
ctx.component('sticker', async (attrs) => {
    const category = attrs.category || 'random';
    const stickerUrl = await db.getSticker(category); // 假设有此函数
    if (!stickerUrl) return h.text(`[找不到 ${category} 分类的表情包]`);
    return h('img', { src: stickerUrl });
});
```
*   **用法**: LLM 只需生成 `给你一个<sticker category="cat"/>`，系统就会自动查询并发送一张猫猫表情包图片。

## 3. 完整工作流程示例

1.  **用户发送 PDF 文件**: 用户发送 `koishi-intro.pdf`。适配器生成 `<file src="https://temp-cdn.com/..."/>`。
2.  **资源持久化 (编码-1)**: 中间件捕获消息，调用 `ctx.assets.create(...)`，下载文件并获得内部 ID `b4c5d6e7`。消息被转换为 `<file id="b4c5d6e7" name="koishi-intro.pdf"/>`，并存入历史记录。
3.  **LLM 交互**: 用户提问：“总结一下这个文件”。
4.  **Agent Tool 调用**: LLM 看到 `<file>` 元素，决定调用 `assets.view_file(id="b4c5d6e7")` 工具。
5.  **系统执行**: `assets.view_file` 函数被调用，它从存储中读取文件，用 `pdf-parse` 提取文本，并将文本内容作为工具的返回结果提供给 LLM。
6.  **生成回复**: LLM 根据提取的文本总结内容，生成纯文本回复。
7.  **发送**: 回复不包含特殊元素，被直接发送给用户。

## 4. LLM 提示词 (Prompt) 指导

在发给 LLM 的 System Prompt 中，必须明确指导其如何理解和使用这些编码后的消息元素。

> 你是一个聊天机器人。你可以使用一种特殊的XML方言来丰富你的回复。
> - **提及用户**: 使用 `<at id="USER_ID"/>`。
> - **回复消息**: 使用 `<quote id="MESSAGE_ID"/>`。
> - **发送图片**: 如果你想发送一张之前对话中出现的图片，可以使用 `<img id="INTERNAL_ID"/>`。
> - **发送表情包**: 使用 `<sticker category="CATEGORY"/>` 来发送一个随机表情包，支持的分类有：`cat`, `dog`, `funny`。
> - **其他格式**: 对于其他复杂内容，请直接以文本形式回复。