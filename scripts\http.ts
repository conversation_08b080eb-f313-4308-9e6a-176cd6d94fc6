import { writeFile } from "fs/promises";

const url = "https://gxh.vip.qq.com/club/item/parcel/item/d2/d2f8463ae55c0186c1fc936cfe551515/raw300.gif";

const response = await fetch(url, {
    method: "GET",
    headers: {
        "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    },
});

console.log(response.status);
console.log(response.statusText);
console.log(response.headers);

await writeFile("test.gif", Buffer.from(await response.arrayBuffer()));
