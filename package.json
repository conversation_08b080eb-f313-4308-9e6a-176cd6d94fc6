{"name": "@root/yesimbot", "version": "0.0.0", "packageManager": "bun@1.2.0", "private": true, "homepage": "https://github.com/HydroGest/YesImBot", "contributors": ["HydroGest <<EMAIL>>", "Dispure <<EMAIL>>", "Miaowfish <<EMAIL>>", "Touch-Night <<EMAIL>>"], "license": "MIT", "workspaces": ["packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "pack": "turbo run pack", "add-change": "changeset add", "version-packages": "changeset version", "release": "bun run build && changeset publish", "create-packages": "changeset version && turbo run pack", "collect-packages": "bun scripts/collect-packages.js"}, "devDependencies": {"@changesets/cli": "^2.29.5", "@types/node": "^22.16.2", "esbuild": "^0.25.6", "glob": "^11.0.3", "tsc-alias": "^1.8.16", "turbo": "2.5.4", "typescript": "^5.8.3", "yml-register": "^1.2.5"}}