## 专属记忆系统设计文档 (Project: Proactive Memory System)

### 1. 核心理念与愿景

本系统旨在超越传统被动问答的AI交互模式，构建一个具备**主动关怀**能力的记忆核心。它围绕三大核心理念设计：

* **隐性捕捉 (Implicit Capture):** 从日常对话中自动、无干扰地提取关键信息（如事实、事件、偏好），无需用户显式下达“记住这个”的指令。
* **情境触发 (Contextual Trigger):** 能在特定时间、特定对话情境下，主动联想并激活相关记忆，使AI的互动更具即时相关性与同理心。
* **长期追踪 (Long-term Follow-up):** 对重要的、有时效性的事件进行持续追踪，在事件结束后适时地进行主动关怀和询问，形成完整的交互闭环。

**最终目标：** 实现如同文中所述的愿景——“当我不经意间说要考试，她会默默记下，在我熬夜时提醒我；几天后，还会主动问我考得怎么样。”

---

### 2. 系统架构

系统采用分层设计，确保各模块职责清晰、易于扩展，由存储层、处理层和应用层构成。

#### 2.1. 存储层 (Storage Layer)

采用面向文档的数据库（如 MongoDB, LokiJS），以用户ID为主键，存储由“记忆卡片 (Memory Cards)”构成的集合。这种结构化存储方式便于精细化管理、检索和实现遗忘机制。

**核心数据结构:** `UserMemory` (详见第四节TS接口定义)

#### 2.2. 处理层 (Processing Layer)

系统的“潜意识”，负责记忆的生成、激活和管理。这是整个系统的引擎。

* **信息捕获器 (Capture Engine):** 监听并预处理消息，聚合形成有意义的“对话片段”。
* **记忆提炼器 (Refinery Engine):** 调用LLM，从“对话片段”中提炼出结构化的 `MemoryCard`，并存入存储层。
* **情境触发器 (Context Trigger Engine):** **【设计重点】** 在每次对话交互前运行，快速扫描用户的 `MemoryCard`，检查是否有符合当前时间和情境的 `trigger` 被激活。这是一个轻量级的匹配引擎，不依赖LLM。
* **长期追踪器 (Follow-up Engine):** **【设计重点】** 以后台定时任务（Cron Job）形式周期性运行（如每小时一次），扫描整个数据库，检查是否有满足条件的 `follow_up` 任务，并将其转化为待办事项。

#### 2.3. 应用层 (Application Layer)

将处理层生成的“记忆”无缝融入到与用户的实际对话中。

* **记忆检索器 (Retrieval Engine):** 根据当前对话上下文，从存储层检索最相关的记忆卡片。
* **记忆简报生成器 (Memory Brief Generator):** 将情境触发器、长期追踪器和记忆检索器的输出，整合成一份简洁的“记忆简报”。
* **Prompt注入器 (Prompt Injector):** 将“记忆简报”动态注入到主LLM的System Prompt中，为AI提供丰富的、即时的上下文和行动建议。

---

### 3. Trigger 与 Follow-Up 机制详解

这是实现主动关怀的核心。我们在 `MemoryCard` 中引入 `trigger` 和 `followUp` 字段，并通过专门的引擎来处理它们。

#### 3.1. Trigger（触发器）

`Trigger` 定义了激活一张记忆卡片的具体条件。当条件满足时，“情境触发器”会生成一条高优先级的“情境提示”，直接影响AI的即时反应。

**触发器类型 (`TriggerType`):**

* `Time`: 仅基于时间的触发。适用于提醒、纪念日等。
    * **场景:** 用户说“下周三是我生日”。
    * **处理:** 在指定日期到来时，触发器激活，生成提示“今天是用户的生日”。
* `Context`: 仅基于对话上下文的触发。适用于基于特定话题的联想。
    * **场景:** 用户提到过“我超爱喝手冲咖啡”。
    * **处理:** 当后续对话中出现“咖啡”、“手冲”、“瑰夏”等关键词时，触发器激活，生成提示“用户是咖啡爱好者，可以深入聊这个话题”。
* `TimeAndContext`: 时间与情境的复合触发。这是实现“深夜关心”场景的关键。
    * **场景:** 用户说“我这周要考试了”。
    * **处理:** `trigger` 会设定一个时间范围（本周）和情境关键词（如“熬夜”、“还在玩”、“没睡”、“好累”）。当用户在此时间范围内发布了包含这些关键词的消息时，触发器激活，生成提示“用户正在备考，现在是深夜，可以关心他为何还不休息”。
* `EventCompletion`: 基于另一事件完成的触发。
    * **场景:** 用户有一个“购买项目A所需材料”的事件卡片和一个“开始组装项目A”的事件卡片。
    * **处理:** 当“购买材料”卡片的状态被更新为 `Archived` 时，可以触发“组装项目”卡片，提醒用户可以开始下一步了。

#### 3.2. Follow-Up（后续追踪）

`FollowUp` 定义了在一个事件结束后，系统应采取的主动追踪动作。“长期追踪器”负责执行这些动作。

**后续追踪类型 (`FollowUpType`):**

* `TimeDelayed`: 在卡片状态变为 `Archived`（或其他终结状态）后，延迟一段时间执行。这是实现“考后追问”场景的关键。
    * **场景:** “PMP考试”这张卡片，在用户说“考完了”之后，其状态被更新为 `Archived`。
    * **处理:** `followUp` 定义了 `delay`（例如3天）和 `prompt`（“询问PMP考试结果”）。3天后，“长期追踪器”会捕获到这个任务，并生成一个待办事项：“主动询问[用户]PMP考试结果”。这个待待办事项会在合适的时机（例如，当AI决定主动发起对话时）被使用。
* `OnStateChange`: 当卡片状态变更为特定状态时立即触发。
    * **场景:** 一个团队协作任务卡片，状态从 `InProgress` 变为 `Blocked`。
    * **处理:** 立即触发一个 `followUp`，生成待办事项“询问[用户]任务受阻的原因，是否需要帮助”。

---
