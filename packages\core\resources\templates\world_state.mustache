{{#date.now}}Current Date: {{.}}{{/date.now}}
{{! ======================================================= }}
{{!              最优先的触发上下文                         }}
{{! ======================================================= }}
<trigger_context>
  {{#triggerContext.isScheduledTask}}
  <system_alert priority="high">
    <reason>SCHEDULED TASK</reason>
    <instructions>
      现在是预定时间，你需要处理以下任务。请忽略最近的聊天内容，优先完成此任务并作出回应。
    </instructions>
    <task_details>
      <name>{{task.taskName}}</name>
      <content>{{task.taskContent}}</content>
    </task_details>
  </system_alert>
  {{/triggerContext.isScheduledTask}}
  {{#triggerContext.isTaskCompletion}}
  <system_alert priority="high">
    <reason>BACKGROUND TASK COMPLETED</reason>
    <instructions>
      你之前启动的一个后台任务已经完成。请根据任务结果，向用户进行汇报或继续下一步计划。
    </instructions>
    <task_result>
      <original_task>{{taskResult.originalTask}}</original_task>
      <status>{{taskResult.status}}</status>
      <result>{{_toString(taskResult.result)}}</result>
    </task_result>
  </system_alert>
  {{/triggerContext.isTaskCompletion}}
  {{#triggerContext.isSystemEvent}}
  <system_alert priority="medium">
    <reason>SYSTEM EVENT DETECTED</reason>
    <instructions>
      群组中发生了一个系统事件，你可以酌情对此作出回应。
    </instructions>
    <event_details>
      <type>{{event.eventType}}</type>
      <details>{{_toString(event.details)}}</details>
    </event_details>
  </system_alert>
  {{/triggerContext.isSystemEvent}}
</trigger_context>
{{! ======================================================= }}
{{!              原有的世界状态（历史记录等）               }}
{{! ======================================================= }}
<world_state>
  # 我所知道的群成员信息
  {{#users}}
  ## 关于 {{id}}({{name}})
  - 核心印象: {{description}}
  {{/users}}
  {{^users.length}}
  ## 我还没有记住任何群成员的信息
  {{/users.length}}
  {{#channel}}
  <channel id="{{id}}" type="{{type}}" platform="{{platform}}">
    <name>{{name}}</name>
    {{#meta.description}}
    <description>{{.}}</description>
    {{/meta.description}}
    {{#members.length}}
    <members>
      {{#members}}
      <user id="{{pid}}"{{#nick}} nick="{{.}}"{{/nick}}{{#roles}} roles="{{_toString}}"{{/roles}}{{#isSelf}} self="true"{{/isSelf}}>{{name}}</user>
      {{/members}}
    </members>
    {{/members.length}}
    <history>
      {{#history.summarized}}
      <segment status="summarized" start="{{startTimestamp}}" end="{{endTimestamp}}">
        <summary>{{summary}}</summary>
      </segment>
      {{/history.summarized}}
      {{#history.folded}}
      <segment status="folded" start="{{startTimestamp}}" end="{{endTimestamp}}">
        {{#dialogue.length}}
        <dialogue>
        {{#dialogue}}
          [{{id}}|{{date}} {{time}}|{{sender.name}}({{sender.id}})]{{#quoteId}} <quote id="{{.}}"/>{{/quoteId}} {{#content}}{{_truncate}}{{/content}}
        {{/dialogue}}
        </dialogue>
        {{/dialogue.length}}
      </segment>
      {{/history.folded}}
      {{#history.closed}}
      <segment status="closed" start="{{startTimestamp}}">
        {{#dialogue.length}}
        <dialogue>
        {{#dialogue}}
          [{{id}}|{{date}} {{time}}|{{sender.name}}({{sender.id}})]{{#quoteId}} <quote id="{{.}}"/>{{/quoteId}} {{#content}}{{_truncate}}{{/content}}
        {{/dialogue}}
        </dialogue>
        {{/dialogue.length}}
        {{#agentTurn.responses.length}}
        {{#agentTurn}}
        <agent_turn timestamp="{{timestamp}}">
          {{#responses}}
          <response>
            <thoughts>
              <observe>{{thoughts.observe}}</observe>
              <analyze_infer>{{thoughts.analyze_infer}}</analyze_infer>
              <plan>{{thoughts.plan}}</plan>
            </thoughts>
            <actions>
              {{#actions}}
              <action>
                <function>{{function}}</function>
                <params>{{_renderParams}}</params>
              </action>
              {{/actions}}
            </actions>
            <observations>
              {{#observations}}
              <observation>
                <function>{{function}}</function>
                <status>{{status}}</status>
                {{#result}}
                <result>{{_toString}}</result>
                {{/result}}
                {{#error}}
                <error>{{_toString}}</error>
                {{/error}}
              </observation>
              {{/observations}}
            </observations>
          </response>
          {{/responses}}
        </agent_turn>
        {{/agentTurn}}
        {{/agentTurn.responses.length}}
      </segment>
      {{/history.closed}}
      {{#history.pending}}
      <segment status="open" start="{{startTimestamp}}"{{#is_current}} is_current="true"{{/is_current}}>
        {{#systemEvents.length}}
        <system_events>
        {{#systemEvents}}
          <event type="{{type}}" timestamp="{{date}}">{{#payload}}{{_toString}}{{/payload}}</event>
        {{/systemEvents}}
        </system_events>
        {{/systemEvents.length}}
        {{#dialogue.length}}
        <dialogue>
        {{#dialogue}}
          [{{id}}|{{date}} {{time}}|{{sender.name}}({{sender.id}})]{{#quoteId}} <quote id="{{.}}"/>{{/quoteId}} {{#content}}{{_truncate}}{{/content}}
        {{/dialogue}}
        </dialogue>
        {{/dialogue.length}}
      </segment>
      {{/history.pending}}
    </history>
  </channel>
  {{/channel}}
</world_state>