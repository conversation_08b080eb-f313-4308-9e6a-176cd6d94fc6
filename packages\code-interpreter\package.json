{"name": "koishi-plugin-yesimbot-extension-code-interpreter", "description": "Yes! I'm <PERSON><PERSON>! 代码执行器扩展插件", "version": "0.0.3", "main": "lib/index.js", "typings": "lib/index.d.ts", "homepage": "https://github.com/HydroGest/YesImBot", "files": ["lib", "dist", "resources"], "scripts": {"build": "tsc && node esbuild.config.mjs", "dev": "tsc -w --preserveWatchOutput", "lint": "eslint . --ext .ts", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo *.tgz", "pack": "bun pm pack"}, "license": "MIT", "contributors": ["MiaowFISH <<EMAIL>>"], "keywords": ["koishi", "plugin", "code", "interpreter", "yesimbot", "extension"], "repository": {"type": "git", "url": "git+https://github.com/HydroGest/YesImBot.git", "directory": "packages/code-interpreter"}, "dependencies": {"vm2": "^3.9.17"}, "devDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "3.0.0-beta.4"}, "peerDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "3.0.0-beta.4"}, "koishi": {"description": {"zh": "为 YesImBot 提供一个安全、隔离的 JavaScript 代码执行器", "en": "Provides a secure and isolated JavaScript code interpreter for the YesImBot"}, "service": {"required": ["yesimbot"]}}}